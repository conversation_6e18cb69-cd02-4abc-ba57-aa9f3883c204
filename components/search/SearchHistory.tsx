'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Sparkles,
  Search,
  ArrowRight,
  Brain
} from 'lucide-react';
import { getIntelligentSearchSuggestions } from '@/lib/search-history';

interface SearchHistoryProps {
  onSuggestionClick: (suggestion: string) => void;
  className?: string;
}

export default function SearchHistory({ onSuggestionClick, className = '' }: SearchHistoryProps) {
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSearchSuggestions();
  }, []);

  const loadSearchSuggestions = async () => {
    try {
      setIsLoading(true);

      // Load intelligent search suggestions
      const suggestions = await getIntelligentSearchSuggestions(4);
      setSearchSuggestions(suggestions);
    } catch (error) {
      console.error('Error loading search suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render anything if we're loading or have no suggestions
  if (isLoading) {
    return (
      <div className={`mb-8 ${className}`}>
        <div className="flex items-center justify-center py-4">
          <div className="flex items-center gap-2 text-slate-500">
            <div className="w-4 h-4 border-2 border-slate-300 border-t-[#FF6800] rounded-full animate-spin" />
            <span className="text-sm">Loading suggestions...</span>
          </div>
        </div>
      </div>
    );
  }

  // If no search suggestions, don't render the component
  if (searchSuggestions.length === 0) {
    return null;
  }

  return (
    <div className={`mb-8 ${className}`}>
      <Card className="border-slate-200 shadow-sm">
        <CardContent className="p-6">
          {/* Search Suggestions */}
          <div>
            <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
              <Brain className="w-5 h-5 text-[#FF6800]" />
              Search Suggestions
            </h3>
            <p className="text-sm text-slate-600 mb-4">
              Intelligent recommendations based on popular topics and search patterns
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {searchSuggestions.map((suggestion, index) => (
                <Button
                  key={`suggestion-${index}`}
                  variant="outline"
                  size="sm"
                  onClick={() => onSuggestionClick(suggestion)}
                  className="justify-start text-left h-auto py-3 px-4 hover:border-[#FF6800] hover:text-[#FF6800] hover:bg-orange-50 transition-all duration-200 group"
                >
                  <div className="flex items-center gap-3 w-full">
                    <Search className="w-4 h-4 text-slate-400 group-hover:text-[#FF6800] transition-colors" />
                    <span className="truncate flex-1 font-medium">{suggestion}</span>
                    <ArrowRight className="w-4 h-4 text-slate-400 group-hover:text-[#FF6800] transition-colors" />
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* AI-Powered Notice */}
          <div className="mt-6 pt-4 border-t border-slate-200">
            <div className="flex items-center gap-2 text-xs text-slate-500">
              <Sparkles className="w-3 h-3 text-[#FF6800]" />
              <span>Suggestions powered by AI analysis of search patterns and knowledge base content</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
